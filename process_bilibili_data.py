"""
处理B站数据的主程序
"""

from data_processor import DataProcessor
import pandas as pd
import numpy as np


def main():
    """
    主函数：处理B站数据
    """
    print("=== 开始处理B站数据 ===")
    
    processor = DataProcessor()
    
    # 处理收集到的数据
    processed_df = processor.process_pipeline(
        'bilibili_data.csv',
        'bilibili_final_data.csv'
    )
    
    if not processed_df.empty:
        print("\n=== 详细数据摘要 ===")
        summary = processor.get_data_summary(processed_df)
        print(f"总记录数: {summary['total_records']}")
        print(f"总列数: {summary['total_columns']}")
        print(f"缺失值总数: {summary['missing_values']}")
        
        # 显示数值字段统计
        print("\n=== 数值字段统计 ===")
        numeric_cols = ['view', 'like', 'coin', 'favorite', 'share', 'reply', 'duration']
        for col in numeric_cols:
            if col in processed_df.columns:
                stats = processed_df[col].describe()
                print(f"{col}:")
                print(f"  平均值: {stats['mean']:.0f}")
                print(f"  中位数: {stats['50%']:.0f}")
                print(f"  最大值: {stats['max']:.0f}")
                print(f"  最小值: {stats['min']:.0f}")
        
        # 显示新增特征统计
        print("\n=== 互动率统计 ===")
        rate_cols = ['like_rate', 'coin_rate', 'favorite_rate', 'share_rate', 'reply_rate', 'interaction_rate']
        for col in rate_cols:
            if col in processed_df.columns:
                print(f"{col}: 平均值={processed_df[col].mean():.4f}, 最大值={processed_df[col].max():.4f}")
        
        # 显示分区分布
        print("\n=== 分区分布 ===")
        if 'tname' in processed_df.columns:
            category_counts = processed_df['tname'].value_counts()
            print(f"共有 {len(category_counts)} 个分区")
            for category, count in category_counts.head(15).items():
                print(f"  {category}: {count} 条 ({count/len(processed_df)*100:.1f}%)")
        
        # 显示时长分类
        print("\n=== 视频时长分布 ===")
        if 'duration_category' in processed_df.columns:
            duration_counts = processed_df['duration_category'].value_counts()
            for category, count in duration_counts.items():
                print(f"  {category}: {count} 条 ({count/len(processed_df)*100:.1f}%)")
        
        # 显示播放量分级
        print("\n=== 播放量分级 ===")
        if 'view_level' in processed_df.columns:
            view_counts = processed_df['view_level'].value_counts()
            for level, count in view_counts.items():
                print(f"  {level}: {count} 条 ({count/len(processed_df)*100:.1f}%)")
        
        # 保存处理后的数据概览
        print("\n=== 保存数据概览 ===")
        
        # 创建数据概览报告
        overview = {
            '数据集基本信息': {
                '总记录数': len(processed_df),
                '总字段数': len(processed_df.columns),
                '数据收集时间': '2025年1月',
                '数据来源': 'B站API + 模拟数据'
            },
            '数值字段统计': {},
            '分区分布': category_counts.to_dict() if 'tname' in processed_df.columns else {},
            '时长分布': duration_counts.to_dict() if 'duration_category' in processed_df.columns else {},
            '播放量分级': view_counts.to_dict() if 'view_level' in processed_df.columns else {}
        }
        
        # 添加数值字段统计
        for col in numeric_cols:
            if col in processed_df.columns:
                overview['数值字段统计'][col] = {
                    '平均值': float(processed_df[col].mean()),
                    '中位数': float(processed_df[col].median()),
                    '最大值': float(processed_df[col].max()),
                    '最小值': float(processed_df[col].min()),
                    '标准差': float(processed_df[col].std())
                }
        
        # 保存概览到JSON文件
        import json
        with open('data_overview.json', 'w', encoding='utf-8') as f:
            json.dump(overview, f, ensure_ascii=False, indent=2)
        
        print("数据概览已保存到: data_overview.json")
        print("处理后的完整数据已保存到: bilibili_final_data.csv")
        
        return processed_df
    
    else:
        print("数据处理失败！")
        return None


if __name__ == "__main__":
    try:
        result = main()
        if result is not None:
            print(f"\n=== 数据预处理完成 ===")
            print(f"最终数据集: {len(result)} 条记录，{len(result.columns)} 个字段")
            print("可以开始进行数据可视化分析！")
        else:
            print("数据预处理失败！")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
