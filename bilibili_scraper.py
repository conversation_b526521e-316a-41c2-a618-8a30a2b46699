"""
哔哩哔哩数据爬虫模块
用于获取B站视频数据，包括视频信息、统计数据等
"""

import requests
import json
import time
import pandas as pd
from typing import List, Dict, Optional
import random


class BilibiliScraper:
    """
    哔哩哔哩数据爬虫类
    
    主要功能：
    - 获取热门视频数据
    - 获取视频详细信息
    - 获取分区视频数据
    """
    
    def __init__(self):
        """初始化爬虫配置"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.bilibili.com/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # B站API接口
        self.api_urls = {
            'popular': 'https://api.bilibili.com/x/web-interface/popular',  # 热门视频
            'ranking': 'https://api.bilibili.com/x/web-interface/ranking/v2',  # 排行榜
            'video_info': 'https://api.bilibili.com/x/web-interface/view',  # 视频详情
            'search': 'https://api.bilibili.com/x/web-interface/search/all/v2',  # 搜索
        }
    
    def get_popular_videos(self, page_size: int = 20) -> List[Dict]:
        """
        获取热门视频数据
        
        Args:
            page_size: 每页视频数量
            
        Returns:
            视频数据列表
        """
        try:
            url = self.api_urls['popular']
            params = {
                'ps': page_size,
                'pn': 1
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data['code'] == 0:
                return data['data']['list']
            else:
                print(f"API返回错误: {data['message']}")
                return []
                
        except Exception as e:
            print(f"获取热门视频失败: {e}")
            return []
    
    def get_ranking_videos(self, tid: int = 0, day: int = 3) -> List[Dict]:
        """
        获取排行榜视频数据
        
        Args:
            tid: 分区ID (0为全站)
            day: 排行榜类型 (1:日榜, 3:三日榜, 7:周榜)
            
        Returns:
            视频数据列表
        """
        try:
            url = self.api_urls['ranking']
            params = {
                'rid': tid,
                'day': day,
                'type': 1,
                'arc_type': 0
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data['code'] == 0:
                return data['data']['list']
            else:
                print(f"API返回错误: {data['message']}")
                return []
                
        except Exception as e:
            print(f"获取排行榜失败: {e}")
            return []
    
    def get_video_detail(self, bvid: str) -> Optional[Dict]:
        """
        获取视频详细信息
        
        Args:
            bvid: 视频BV号
            
        Returns:
            视频详细信息
        """
        try:
            url = self.api_urls['video_info']
            params = {'bvid': bvid}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data['code'] == 0:
                return data['data']
            else:
                print(f"获取视频详情失败: {data['message']}")
                return None
                
        except Exception as e:
            print(f"获取视频详情异常: {e}")
            return None
    
    def search_videos(self, keyword: str, page: int = 1, page_size: int = 20) -> List[Dict]:
        """
        搜索视频
        
        Args:
            keyword: 搜索关键词
            page: 页码
            page_size: 每页数量
            
        Returns:
            搜索结果列表
        """
        try:
            url = self.api_urls['search']
            params = {
                'keyword': keyword,
                'page': page,
                'page_size': page_size,
                'order': 'totalrank',  # 综合排序
                'duration': 0,
                'tids': 0
            }
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data['code'] == 0 and 'result' in data['data']:
                return data['data']['result'].get('video', [])
            else:
                print(f"搜索失败: {data.get('message', '未知错误')}")
                return []
                
        except Exception as e:
            print(f"搜索异常: {e}")
            return []
    
    def extract_video_data(self, video_list: List[Dict]) -> pd.DataFrame:
        """
        提取并标准化视频数据
        
        Args:
            video_list: 原始视频数据列表
            
        Returns:
            标准化的DataFrame
        """
        extracted_data = []
        
        for video in video_list:
            try:
                # 提取基本信息
                video_data = {
                    'bvid': video.get('bvid', ''),
                    'aid': video.get('aid', 0),
                    'title': video.get('title', ''),
                    'desc': video.get('desc', ''),
                    'duration': video.get('duration', 0),
                    'pubdate': video.get('pubdate', 0),
                    'ctime': video.get('ctime', 0),
                    'view': video.get('stat', {}).get('view', 0),
                    'danmaku': video.get('stat', {}).get('danmaku', 0),
                    'reply': video.get('stat', {}).get('reply', 0),
                    'favorite': video.get('stat', {}).get('favorite', 0),
                    'coin': video.get('stat', {}).get('coin', 0),
                    'share': video.get('stat', {}).get('share', 0),
                    'like': video.get('stat', {}).get('like', 0),
                    'owner_name': video.get('owner', {}).get('name', ''),
                    'owner_mid': video.get('owner', {}).get('mid', 0),
                    'tname': video.get('tname', ''),
                    'tid': video.get('tid', 0),
                    'pic': video.get('pic', ''),
                }
                
                extracted_data.append(video_data)
                
            except Exception as e:
                print(f"提取视频数据失败: {e}")
                continue
        
        return pd.DataFrame(extracted_data)
    
    def delay_request(self, min_delay: float = 0.5, max_delay: float = 2.0):
        """
        请求延迟，避免被限制
        
        Args:
            min_delay: 最小延迟时间（秒）
            max_delay: 最大延迟时间（秒）
        """
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)


if __name__ == "__main__":
    # 测试代码
    scraper = BilibiliScraper()
    
    print("正在获取热门视频数据...")
    popular_videos = scraper.get_popular_videos(20)
    
    if popular_videos:
        df = scraper.extract_video_data(popular_videos)
        print(f"成功获取 {len(df)} 条视频数据")
        print(df.head())
        
        # 保存数据
        df.to_csv('bilibili_popular_videos.csv', index=False, encoding='utf-8-sig')
        print("数据已保存到 bilibili_popular_videos.csv")
    else:
        print("未获取到数据")
